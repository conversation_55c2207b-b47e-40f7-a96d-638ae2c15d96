#include <cuda_runtime.h>

#include <iostream>

using namespace std;

constexpr int input_rows = 500;
constexpr int input_cols = 500;
constexpr int kernel_rows = 3;
constexpr int kernel_cols = 3;

constexpr int BLOCK_SIZE = 16;

// 非共享内存版
__global__ void GaussianBlur_Base(const float *__restrict input, const float *__restrict kernel,
                                  float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;
    if (idx >= 500 || idy >= 500)
        return;

    float sum = 0.0f;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            sum += input[(idy + i) * 502 + idx + j] * kernel[i * 3 + j];
        }
    }
    output[idy * 500 + idx] = sum;
}

// 共享内存版
__global__ void GaussianBlur_Shm(const float *__restrict input, const float *__restrict kernel,
                                 float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;
    if (idx >= 500 || idy >= 500)
        return;

    auto index = [](int row, int col, int n) { return row * n + col };
    __shared__ float shm_in[18][18];
    for (int i = 0; i < 18; i++)
        for (int j = 0; j < 18; j++)
            if (index(idy, idx, 18) < 502 * 502)
                shm_in[by + i][bx + j] = input[index(idy, idx, 18)];

    __shared__ float shm_ker[9];
    for (int i = 0; i < 9; i++)
        shm_ker[i] = kernel[i];

    float sum = 0.0f;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            sum += shm_in[index(by, bx, 18)] * kernel[index(i, j, 3)];
        }
    }
    output[index(idy, idx, 500)] = sum;
}

// 常量内存版
__constant__ float const_kernel[9];
// 共享内存版
__global__ void GaussianBlur_Cst(const float *__restrict input, float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;
    if (idx >= 500 || idy >= 500)
        return;

    auto index = [](int row, int col, int n) { return row * n + col };
    __shared__ float shm_in[18][18];
    for (int i = 0; i < 18; i++)
        for (int j = 0; j < 18; j++)
            if (index(idy, idx, 18) < 502 * 502)
                shm_in[by + i][bx + j] = input[index(idy, idx, 18)];

    float sum = 0.0f;
    for (int i = 0; i < 3; i++) {
        for (int j = 0; j < 3; j++) {
            sum += shm_in[index(by, bx, 18)] * const_kernel[index(i, j, 3)];
        }
    }
    output[index(idy, idx, 500)] = sum;
}

int main() {
    int N = input_rows * input_cols;
    float *input = new float[N];
    float *output = new float[N];
    for (int i = 0; i < N; i++)
        input[i] = rand() % 11;

    float kernel[] = {0.0625, 0.125, 0.0625, 0.125, 0.25, 0.125, 0.0625, 0.125, 0.0625};

    int N_P = (input_rows + 2) * (input_cols + 2);
    float *input_padding = new float[N_P];

    float *ptr_in_pad = input_padding + 502 + 1;
    float *ptr_in = input;
    for (int i = 0; i < 500; i++) {
        memcpy(ptr_in_pad, ptr_in, 500 * sizeof(float));
        ptr_in_pad += 502;
        ptr_in += 500;
    }

    float *d_input;
    float *d_output;
    float *d_kernel;
    cudaMalloc(&d_input, N_P * sizeof(float));
    cudaMalloc(&d_output, N * sizeof(float));
    cudaMalloc(&d_kernel, kernel_rows * kernel_cols * sizeof(float));

    cudaMemcpy(d_input, input_padding, N_P * sizeof(float), cudaMemcpyHostToDevice);
    cudaMemcpy(d_kernel, kernel, kernel_rows * kernel_cols * sizeof(float), cudaMemcpyHostToDevice);

    dim3 threads(BLOCK_SIZE, BLOCK_SIZE, 1);
    dim3 blocks((500 + BLOCK_SIZE - 1) / BLOCK_SIZE, (500 + BLOCK_SIZE - 1) / BLOCK_SIZE, 1);
    GaussianBlur_Base<<<blocks, threads>>>(d_input, d_kernel, d_output);
    cudaDeviceSynchronize();

    cudaMemcpy(output, d_output, N * sizeof(float), cudaMemcpyDeviceToHost);
}