#include <cuda_runtime.h>

#include <chrono>
#include <cmath>
#include <cstring>
#include <iostream>

using namespace std;

// 错误检查宏
#define CHECK(call)                                                                                \
    do {                                                                                           \
        cudaError_t err = call;                                                                    \
        if (err != cudaSuccess) {                                                                  \
            cerr << "Error: " << cudaGetErrorString(err) << " in file " << __FILE__ << " at line " \
                 << __LINE__ << endl;                                                              \
            exit(EXIT_FAILURE);                                                                    \
        }                                                                                          \
    } while (0)

constexpr int input_rows = 500;
constexpr int input_cols = 500;
constexpr int output_rows = input_rows;
constexpr int output_cols = input_cols;
constexpr int kernel_rows = 3;
constexpr int kernel_cols = 3;

constexpr int BLOCK_SIZE = 16;

// CPU版本高斯模糊，用于验证结果
void GaussianBlur_CPU(const float *input, const float *kernel, float *output) {
    for (int y = 0; y < input_rows; y++) {
        for (int x = 0; x < input_cols; x++) {
            float sum = 0.0f;
            for (int i = 0; i < kernel_rows; i++) {
                for (int j = 0; j < kernel_cols; j++) {
                    if (y - 1 + i < 0 || x - 1 + j < 0 || y - 1 + i >= input_rows ||
                        x - 1 + j >= input_cols)
                        continue;
                    sum +=
                        input[(y - 1 + i) * input_cols + (x - 1 + j)] * kernel[i * kernel_cols + j];
                }
            }
            output[y * input_cols + x] = sum;
        }
    }
}

// 非共享内存版
__global__ void GaussianBlur_Base(const float *__restrict__ input, const float *__restrict__ kernel,
                                  float *output) {
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int x = blockDim.x * blockIdx.x + tx;
    int y = blockDim.y * blockIdx.y + ty;

    if (x >= output_cols || y >= output_rows)
        return;
    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            if (y - 1 + i < 0 || x - 1 + j < 0 || y - 1 + i >= input_rows ||
                x - 1 + j >= input_cols)
                continue;
            sum += input[(y - 1 + i) * input_cols + x - 1 + j] * kernel[i * kernel_cols + j];
        }
    }
    output[(y)*output_cols + x] = sum;
}

// 共享内存版
__global__ void GaussianBlur_Shm(const float *__restrict__ input, const float *__restrict__ kernel,
                                 float *output) {
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int x = blockDim.x * blockIdx.x + tx;
    int y = blockDim.y * blockIdx.y + ty;

    if (x >= input_cols || y >= input_rows)
        return;

    auto index = [](int row, int col, int n) { return row * n + col; };

    // 共享内存大小：BLOCK_SIZE + 2（为了存储边界像素）
    __shared__ float shm_in[BLOCK_SIZE + 2][BLOCK_SIZE + 2];

    shm_in[ty + 1][tx + 1] = input[index(y, x, input_cols)];

    if (tx == 0)
        if (x - 1 >= 0)
            shm_in[ty + 1][tx] = input[index(y, x - 1, input_cols)];
        else
            shm_in[ty + 1][tx] = 0.0f;
    if (ty == 0)
        if (y - 1 >= 0)
            shm_in[ty][tx + 1] = input[index(y - 1, x, input_cols)];
        else
            shm_in[ty][tx + 1] = 0.0f;
    if (tx == 0 && ty == 0)
        if (x - 1 >= 0 && y - 1 >= 0)
            shm_in[ty][tx] = input[index(y - 1, x - 1, input_cols)];
        else
            shm_in[ty][tx] = 0.0f;
    if (tx == BLOCK_SIZE - 1)
        if (x + 1 < input_cols)
            shm_in[ty + 1][tx + 2] = input[index(y, x + 1, input_cols)];
        else
            shm_in[ty + 1][tx + 2] = 0.0f;
    if (ty == BLOCK_SIZE - 1)
        if (y + 1 < input_rows)
            shm_in[ty + 2][tx + 1] = input[index(y + 1, x, input_cols)];
        else
            shm_in[ty + 2][tx + 1] = 0.0f;
    if (ty == BLOCK_SIZE - 1 && tx == BLOCK_SIZE - 1)
        if (x + 1 < input_cols && y + 1 < input_rows)
            shm_in[ty + 2][tx + 2] = input[index(y + 1, x + 1, input_cols)];
        else
            shm_in[ty + 2][tx + 2] = 0.0f;
    if (tx == 0 && ty == BLOCK_SIZE - 1)
        if (x - 1 >= 0 && y + 1 < input_rows)
            shm_in[ty + 2][tx] = input[index(y + 1, x - 1, input_cols)];
        else
            shm_in[ty + 2][tx] = 0.0f;
    if (ty == 0 && tx == BLOCK_SIZE - 1)
        if (x + 1 < input_cols && y - 1 >= 0)
            shm_in[ty][tx + 2] = input[index(y - 1, x + 1, input_cols)];
        else
            shm_in[ty][tx + 2] = 0.0f;
    if (x == input_cols - 1)
        shm_in[ty + 1][tx + 2] = 0.0f;
    if (y == input_rows - 1)
        shm_in[ty + 2][tx + 1] = 0.0f;
    if (ty == 0 && x == input_cols - 1)
        shm_in[ty][tx + 2] = 0.0f;
    if (y == input_rows - 1 && tx == 0)
        shm_in[ty + 2][tx] = 0.0f;
    if (x == input_cols - 1 && y == input_rows - 1)
        shm_in[ty + 2][tx + 2] = 0.0f;
    __shared__ float shm_ker[kernel_rows * kernel_cols];
    if (tx < kernel_cols && ty < kernel_rows) {
        shm_ker[ty * kernel_cols + tx] = kernel[ty * kernel_cols + tx];
    }
    __syncthreads();

    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            sum += shm_in[ty + i][tx + j] * shm_ker[i * kernel_cols + j];
        }
    }
    output[index(y, x, input_cols)] = sum;
}

// 常量内存版
__constant__ float const_kernel[kernel_rows * kernel_cols];

__global__ void GaussianBlur_Cst(const float *__restrict__ input, float *output) {
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int x = blockDim.x * blockIdx.x + tx;
    int y = blockDim.y * blockIdx.y + ty;

    if (x >= input_cols || y >= input_rows)
        return;

    auto index = [](int row, int col, int n) { return row * n + col; };

    // 共享内存大小：BLOCK_SIZE + 2（为了存储边界像素）
    __shared__ float shm_in[BLOCK_SIZE + 2][BLOCK_SIZE + 2];

    // 加载当前块及边界像素到共享内存
    x = x - 1;  // 向左偏移1个像素
    y = y - 1;  // 向上偏移1个像素

    // 确保不越界访问
    if (x >= 0 && x < input_cols && y >= 0 && y < input_rows + 2) {
        shm_in[ty + 1][tx + 1] = input[index(y, x, input_cols)];
    } else {
        shm_in[ty + 1][tx + 1] = 0.0f;
    }

    // 加载额外的边界像素
    if (tx == 0 && x > 0) {
        shm_in[ty + 1][0] = input[index(y, x - 1, input_cols)];
    }
    if (tx == BLOCK_SIZE - 1 && x < input_cols) {
        shm_in[ty + 1][BLOCK_SIZE + 1] = input[index(y, x + 1, input_cols)];
    }
    if (ty == 0 && y > 0) {
        shm_in[0][tx + 1] = input[index(y - 1, x, input_cols)];
    }
    if (ty == BLOCK_SIZE - 1 && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][tx + 1] = input[index(y + 1, x, input_cols)];
    }

    // 加载角落像素
    if (tx == 0 && ty == 0 && x > 0 && y > 0) {
        shm_in[0][0] = input[index(y - 1, x - 1, input_cols)];
    }
    if (tx == BLOCK_SIZE - 1 && ty == 0 && x < input_cols && y > 0) {
        shm_in[0][BLOCK_SIZE + 1] = input[index(y - 1, x + 1, input_cols)];
    }
    if (tx == 0 && ty == BLOCK_SIZE - 1 && x > 0 && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][0] = input[index(y + 1, x - 1, input_cols)];
    }
    if (tx == BLOCK_SIZE - 1 && ty == BLOCK_SIZE - 1 && x < input_cols && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][BLOCK_SIZE + 1] = input[index(y + 1, x + 1, input_cols)];
    }

    __syncthreads();

    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            sum += shm_in[ty + i][tx + j] * const_kernel[i * kernel_cols + j];
        }
    }
    output[index(y, x, input_cols)] = sum;
}

// 验证结果是否正确
bool verify_results(const float *gpu_result, const float *cpu_result, int size,
                    float epsilon = 1e-5f) {
    for (int i = 0; i < size; i++) {
        if (fabs(gpu_result[i] - cpu_result[i]) > epsilon) {
            cerr << "结果不匹配 at index " << i << ": GPU=" << gpu_result[i]
                 << ", CPU=" << cpu_result[i] << endl;
            return false;
        }
    }
    return true;
}

int main() {
    int N = input_rows * input_cols;
    float *h_input = new float[N];
    float *h_output_base = new float[N];
    float *h_output_shm = new float[N];
    float *h_output_cst = new float[N];
    float *h_output_cpu = new float[N];

    // 生成随机输入数据
    for (int i = 0; i < N; i++) {
        h_input[i] = i * 0.1f;  // 0-10之间的随机数
    }

    // 高斯核
    float kernel[] = {0.0625f, 0.125f, 0.0625f, 0.125f, 0.25f, 0.125f, 0.0625f, 0.125f, 0.0625f};

    // CPU计算（作为参考）
    auto start_cpu = chrono::high_resolution_clock::now();
    GaussianBlur_CPU(h_input, kernel, h_output_cpu);
    auto end_cpu = chrono::high_resolution_clock::now();
    chrono::duration<double> cpu_time = end_cpu - start_cpu;
    cout << "CPU 计算时间: " << cpu_time.count() * 1000 << " ms" << endl;

    // 设备内存分配
    float *d_input, *d_output_base, *d_output_shm, *d_output_cst, *d_kernel;
    CHECK(cudaMalloc(&d_input, N * sizeof(float)));
    CHECK(cudaMalloc(&d_output_base, N * sizeof(float)));
    CHECK(cudaMalloc(&d_output_shm, N * sizeof(float)));
    CHECK(cudaMalloc(&d_output_cst, N * sizeof(float)));
    CHECK(cudaMalloc(&d_kernel, kernel_rows * kernel_cols * sizeof(float)));

    // 数据复制到设备
    CHECK(cudaMemcpy(d_input, h_input, N * sizeof(float), cudaMemcpyHostToDevice));
    CHECK(cudaMemcpy(
        d_kernel, kernel, kernel_rows * kernel_cols * sizeof(float), cudaMemcpyHostToDevice));
    CHECK(cudaMemcpyToSymbol(const_kernel, kernel, kernel_rows * kernel_cols * sizeof(float)));

    // 配置线程块和网格
    dim3 threads(BLOCK_SIZE, BLOCK_SIZE, 1);
    dim3 blocks(
        (input_cols + BLOCK_SIZE - 1) / BLOCK_SIZE, (input_rows + BLOCK_SIZE - 1) / BLOCK_SIZE, 1);

    // 测试基础版本
    auto start_base = chrono::high_resolution_clock::now();
    GaussianBlur_Base<<<blocks, threads>>>(d_input, d_kernel, d_output_base);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_base = chrono::high_resolution_clock::now();
    chrono::duration<double> base_time = end_base - start_base;

    // 复制结果回主机
    CHECK(cudaMemcpy(h_output_base, d_output_base, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证基础版本结果
    bool base_valid = verify_results(h_output_base, h_output_cpu, N);
    cout << "基础版本 " << (base_valid ? "验证成功" : "验证失败") << endl;
    cout << "基础版本计算时间: " << base_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / base_time.count() << "x)" << endl;

    // 测试共享内存版本
    auto start_shm = chrono::high_resolution_clock::now();
    GaussianBlur_Shm<<<blocks, threads>>>(d_input, d_kernel, d_output_shm);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_shm = chrono::high_resolution_clock::now();
    chrono::duration<double> shm_time = end_shm - start_shm;

    // 复制结果回主机
    CHECK(cudaMemcpy(h_output_shm, d_output_shm, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证共享内存版本结果
    bool shm_valid = verify_results(h_output_shm, h_output_cpu, N);
    cout << "共享内存版本 " << (shm_valid ? "验证成功" : "验证失败") << endl;
    cout << "共享内存版本计算时间: " << shm_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / shm_time.count()
         << "x, 相对基础版本: " << base_time.count() / shm_time.count() << "x)" << endl;

    // 测试常量内存版本
    auto start_cst = chrono::high_resolution_clock::now();
    GaussianBlur_Cst<<<blocks, threads>>>(d_input, d_output_cst);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_cst = chrono::high_resolution_clock::now();
    chrono::duration<double> cst_time = end_cst - start_cst;

    // 复制结果回主机
    CHECK(cudaMemcpy(h_output_cst, d_output_cst, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证常量内存版本结果
    bool cst_valid = verify_results(h_output_cst, h_output_cpu, N);
    cout << "常量内存版本 " << (cst_valid ? "验证成功" : "验证失败") << endl;
    cout << "常量内存版本计算时间: " << cst_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / cst_time.count()
         << "x, 相对基础版本: " << base_time.count() / cst_time.count() << "x)" << endl;

    // 释放设备内存
    CHECK(cudaFree(d_input));
    CHECK(cudaFree(d_output_base));
    CHECK(cudaFree(d_output_shm));
    CHECK(cudaFree(d_output_cst));
    CHECK(cudaFree(d_kernel));

    // 释放主机内存
    delete[] h_input;
    delete[] h_output_base;
    delete[] h_output_shm;
    delete[] h_output_cst;
    delete[] h_output_cpu;

    cout << "所有操作完成" << endl;
    return 0;
}
