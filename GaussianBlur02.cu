#include <cuda_runtime.h>

#include <chrono>
#include <cmath>
#include <cstring>
#include <iostream>

using namespace std;

// 错误检查宏
#define CHECK(call)                                                                                \
    do {                                                                                           \
        cudaError_t err = call;                                                                    \
        if (err != cudaSuccess) {                                                                  \
            cerr << "Error: " << cudaGetErrorString(err) << " in file " << __FILE__ << " at line " \
                 << __LINE__ << endl;                                                              \
            exit(EXIT_FAILURE);                                                                    \
        }                                                                                          \
    } while (0)

constexpr int input_rows = 500;
constexpr int input_cols = 500;
constexpr int kernel_rows = 3;
constexpr int kernel_cols = 3;

constexpr int BLOCK_SIZE = 16;

// CPU版本高斯模糊，用于验证结果
void GaussianBlur_CPU(const float *input, const float *kernel, float *output) {
    for (int y = 0; y < input_rows; y++) {
        for (int x = 0; x < input_cols; x++) {
            float sum = 0.0f;
            for (int i = 0; i < kernel_rows; i++) {
                for (int j = 0; j < kernel_cols; j++) {
                    // 输入已经做了边界填充，这里直接访问
                    sum +=
                        input[(y + i) * (input_cols + 2) + (x + j)] * kernel[i * kernel_cols + j];
                }
            }
            output[y * input_cols + x] = sum;
        }
    }
}

// 非共享内存版
__global__ void GaussianBlur_Base(const float *__restrict__ input, const float *__restrict__ kernel,
                                  float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;

    if (idx >= input_cols || idy >= input_rows)
        return;

    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            sum += input[(idy + i) * (input_cols + 2) + idx + j] * kernel[i * kernel_cols + j];
        }
    }
    output[idy * input_cols + idx] = sum;
}

// 共享内存版
__global__ void GaussianBlur_Shm(const float *__restrict__ input, const float *__restrict__ kernel,
                                 float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;

    if (idx >= input_cols || idy >= input_rows)
        return;

    auto index = [](int row, int col, int n) { return row * n + col; };

    // 共享内存大小：BLOCK_SIZE + 2（为了存储边界像素）
    __shared__ float shm_in[BLOCK_SIZE + 2][BLOCK_SIZE + 2];

    shm_in[by][bx] = input[index(idy, idx, input_cols + 2)];
    if (by == BLOCK_SIZE - 1) {
        if (idy + 1 < input_rows + 2)
            shm_in[by + 1][bx] = input[index(idy + 1, idx, input_cols + 2)];
        if (idy + 2 < input_rows + 2)
            shm_in[by + 2][bx] = input[index(idy + 2, idx, input_cols + 2)];
    }

    if (idy == input_rows - 1) {
        shm_in[by + 1][bx] = input[index(idy + 1, idx, input_cols + 2)];
        shm_in[by + 2][bx] = input[index(idy + 2, idx, input_cols + 2)];
    }
    if (idx == input_cols - 1) {
        shm_in[by][bx + 1] = input[index(idy, idx + 1, input_cols + 2)];
        shm_in[by][bx + 2] = input[index(idy, idx + 2, input_cols + 2)];
    }
    if (idy == input_rows - 1 && idx == input_cols - 1) {
        shm_in[by + 1][bx + 1] = input[index(idy + 1, idx + 1, input_cols + 2)];
        shm_in[by + 1][bx + 2] = input[index(idy + 1, idx + 2, input_cols + 2)];
        shm_in[by + 2][bx + 1] = input[index(idy + 2, idx + 1, input_cols + 2)];
        shm_in[by + 2][bx + 2] = input[index(idy + 2, idx + 2, input_cols + 2)];
    }

    if (bx == BLOCK_SIZE - 1) {
        if (idx + 1 < input_cols + 2)
            shm_in[by][bx + 1] = input[index(idy, idx + 1, input_cols + 2)];
        if (idx + 2 < input_cols + 2)
            shm_in[by][bx + 2] = input[index(idy, idx + 2, input_cols + 2)];
    }

    if (by == BLOCK_SIZE - 1 && by == BLOCK_SIZE - 1) {
        if (idy + 1 < input_rows + 2 && idx + 1 < input_cols + 2)
            shm_in[by + 1][bx + 1] = input[index(idy + 1, idx + 1, input_cols + 2)];
        if (idy + 1 < input_rows + 2 && idx + 2 < input_cols + 2)
            shm_in[by + 1][bx + 2] = input[index(idy + 1, idx + 2, input_cols + 2)];
        if (idy + 2 < input_rows + 2 && idx + 1 < input_cols + 2)
            shm_in[by + 2][bx + 1] = input[index(idy + 2, idx + 1, input_cols + 2)];
        if (idy + 2 < input_rows + 2 && idx + 2 < input_cols + 2)
            shm_in[by + 2][bx + 2] = input[index(idy + 2, idx + 2, input_cols + 2)];
    }

    __shared__ float shm_ker[kernel_rows * kernel_cols];
    if (bx < kernel_cols && by < kernel_rows) {
        shm_ker[by * kernel_cols + bx] = kernel[by * kernel_cols + bx];
    }
    __syncthreads();

    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            sum += shm_in[by + i][bx + j] * shm_ker[i * kernel_cols + j];
        }
    }
    output[index(idy, idx, input_cols)] = sum;
}

// 常量内存版
__constant__ float const_kernel[kernel_rows * kernel_cols];

__global__ void GaussianBlur_Cst(const float *__restrict__ input, float *output) {
    int bx = threadIdx.x;
    int by = threadIdx.y;
    int idx = blockDim.x * blockIdx.x + bx;
    int idy = blockDim.y * blockIdx.y + by;

    if (idx >= input_cols || idy >= input_rows)
        return;

    auto index = [](int row, int col, int n) { return row * n + col; };

    // 共享内存大小：BLOCK_SIZE + 2（为了存储边界像素）
    __shared__ float shm_in[BLOCK_SIZE + 2][BLOCK_SIZE + 2];

    // 加载当前块及边界像素到共享内存
    int x = idx - 1;  // 向左偏移1个像素
    int y = idy - 1;  // 向上偏移1个像素

    // 确保不越界访问
    if (x >= 0 && x < input_cols + 2 && y >= 0 && y < input_rows + 2) {
        shm_in[by + 1][bx + 1] = input[index(y, x, input_cols + 2)];
    } else {
        shm_in[by + 1][bx + 1] = 0.0f;
    }

    // 加载额外的边界像素
    if (bx == 0 && x > 0) {
        shm_in[by + 1][0] = input[index(y, x - 1, input_cols + 2)];
    }
    if (bx == BLOCK_SIZE - 1 && x < input_cols) {
        shm_in[by + 1][BLOCK_SIZE + 1] = input[index(y, x + 1, input_cols + 2)];
    }
    if (by == 0 && y > 0) {
        shm_in[0][bx + 1] = input[index(y - 1, x, input_cols + 2)];
    }
    if (by == BLOCK_SIZE - 1 && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][bx + 1] = input[index(y + 1, x, input_cols + 2)];
    }

    // 加载角落像素
    if (bx == 0 && by == 0 && x > 0 && y > 0) {
        shm_in[0][0] = input[index(y - 1, x - 1, input_cols + 2)];
    }
    if (bx == BLOCK_SIZE - 1 && by == 0 && x < input_cols && y > 0) {
        shm_in[0][BLOCK_SIZE + 1] = input[index(y - 1, x + 1, input_cols + 2)];
    }
    if (bx == 0 && by == BLOCK_SIZE - 1 && x > 0 && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][0] = input[index(y + 1, x - 1, input_cols + 2)];
    }
    if (bx == BLOCK_SIZE - 1 && by == BLOCK_SIZE - 1 && x < input_cols && y < input_rows) {
        shm_in[BLOCK_SIZE + 1][BLOCK_SIZE + 1] = input[index(y + 1, x + 1, input_cols + 2)];
    }

    __syncthreads();

    float sum = 0.0f;
    for (int i = 0; i < kernel_rows; i++) {
        for (int j = 0; j < kernel_cols; j++) {
            sum += shm_in[by + i][bx + j] * const_kernel[i * kernel_cols + j];
        }
    }
    output[index(idy, idx, input_cols)] = sum;
}

// 验证结果是否正确
bool verify_results(const float *gpu_result, const float *cpu_result, int size,
                    float epsilon = 1e-5f) {
    for (int i = 0; i < size; i++) {
        if (fabs(gpu_result[i] - cpu_result[i]) > epsilon) {
            cerr << "结果不匹配 at index " << i << ": GPU=" << gpu_result[i]
                 << ", CPU=" << cpu_result[i] << endl;
            return false;
        }
    }
    return true;
}

int main() {
    int N = input_rows * input_cols;
    float *input = new float[N];
    float *output_base = new float[N];
    float *output_shm = new float[N];
    float *output_cst = new float[N];
    float *output_cpu = new float[N];

    // 生成随机输入数据
    for (int i = 0; i < N; i++) {
        input[i] = i * 0.1f;  // 0-10之间的随机数
    }

    // 高斯核
    float kernel[] = {0.0625f, 0.125f, 0.0625f, 0.125f, 0.25f, 0.125f, 0.0625f, 0.125f, 0.0625f};

    // 创建带边界填充的输入
    int padded_rows = input_rows + 2;
    int padded_cols = input_cols + 2;
    int N_P = padded_rows * padded_cols;
    float *input_padding = new float[N_P]();  // 初始化为0

    // 填充数据（边界已经初始化为0）
    float *ptr_in_pad = input_padding + padded_cols + 1;  // 跳过第一行和第一列
    float *ptr_in = input;
    for (int i = 0; i < input_rows; i++) {
        memcpy(ptr_in_pad, ptr_in, input_cols * sizeof(float));
        ptr_in_pad += padded_cols;
        ptr_in += input_cols;
    }

    // CPU计算（作为参考）
    auto start_cpu = chrono::high_resolution_clock::now();
    GaussianBlur_CPU(input_padding, kernel, output_cpu);
    auto end_cpu = chrono::high_resolution_clock::now();
    chrono::duration<double> cpu_time = end_cpu - start_cpu;
    cout << "CPU 计算时间: " << cpu_time.count() * 1000 << " ms" << endl;

    // 设备内存分配
    float *d_input, *d_output_base, *d_output_shm, *d_output_cst, *d_kernel;
    CHECK(cudaMalloc(&d_input, N_P * sizeof(float)));
    CHECK(cudaMalloc(&d_output_base, N * sizeof(float)));
    CHECK(cudaMalloc(&d_output_shm, N * sizeof(float)));
    CHECK(cudaMalloc(&d_output_cst, N * sizeof(float)));
    CHECK(cudaMalloc(&d_kernel, kernel_rows * kernel_cols * sizeof(float)));

    // 数据复制到设备
    CHECK(cudaMemcpy(d_input, input_padding, N_P * sizeof(float), cudaMemcpyHostToDevice));
    CHECK(cudaMemcpy(
        d_kernel, kernel, kernel_rows * kernel_cols * sizeof(float), cudaMemcpyHostToDevice));
    CHECK(cudaMemcpyToSymbol(const_kernel, kernel, kernel_rows * kernel_cols * sizeof(float)));

    // 配置线程块和网格
    dim3 threads(BLOCK_SIZE, BLOCK_SIZE, 1);
    dim3 blocks(
        (input_cols + BLOCK_SIZE - 1) / BLOCK_SIZE, (input_rows + BLOCK_SIZE - 1) / BLOCK_SIZE, 1);

    // 测试基础版本
    auto start_base = chrono::high_resolution_clock::now();
    GaussianBlur_Base<<<blocks, threads>>>(d_input, d_kernel, d_output_base);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_base = chrono::high_resolution_clock::now();
    chrono::duration<double> base_time = end_base - start_base;

    // 复制结果回主机
    CHECK(cudaMemcpy(output_base, d_output_base, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证基础版本结果
    bool base_valid = verify_results(output_base, output_cpu, N);
    cout << "基础版本 " << (base_valid ? "验证成功" : "验证失败") << endl;
    cout << "基础版本计算时间: " << base_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / base_time.count() << "x)" << endl;

    // 测试共享内存版本
    auto start_shm = chrono::high_resolution_clock::now();
    GaussianBlur_Shm<<<blocks, threads>>>(d_input, d_kernel, d_output_shm);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_shm = chrono::high_resolution_clock::now();
    chrono::duration<double> shm_time = end_shm - start_shm;

    // 复制结果回主机
    CHECK(cudaMemcpy(output_shm, d_output_shm, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证共享内存版本结果
    bool shm_valid = verify_results(output_shm, output_cpu, N);
    cout << "共享内存版本 " << (shm_valid ? "验证成功" : "验证失败") << endl;
    cout << "共享内存版本计算时间: " << shm_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / shm_time.count()
         << "x, 相对基础版本: " << base_time.count() / shm_time.count() << "x)" << endl;

    // 测试常量内存版本
    auto start_cst = chrono::high_resolution_clock::now();
    GaussianBlur_Cst<<<blocks, threads>>>(d_input, d_output_cst);
    CHECK(cudaGetLastError());
    CHECK(cudaDeviceSynchronize());
    auto end_cst = chrono::high_resolution_clock::now();
    chrono::duration<double> cst_time = end_cst - start_cst;

    // 复制结果回主机
    CHECK(cudaMemcpy(output_cst, d_output_cst, N * sizeof(float), cudaMemcpyDeviceToHost));

    // 验证常量内存版本结果
    bool cst_valid = verify_results(output_cst, output_cpu, N);
    cout << "常量内存版本 " << (cst_valid ? "验证成功" : "验证失败") << endl;
    cout << "常量内存版本计算时间: " << cst_time.count() * 1000 << " ms"
         << " (加速比: " << cpu_time.count() / cst_time.count()
         << "x, 相对基础版本: " << base_time.count() / cst_time.count() << "x)" << endl;

    // 释放设备内存
    CHECK(cudaFree(d_input));
    CHECK(cudaFree(d_output_base));
    CHECK(cudaFree(d_output_shm));
    CHECK(cudaFree(d_output_cst));
    CHECK(cudaFree(d_kernel));

    // 释放主机内存
    delete[] input;
    delete[] output_base;
    delete[] output_shm;
    delete[] output_cst;
    delete[] output_cpu;
    delete[] input_padding;

    cout << "所有操作完成" << endl;
    return 0;
}
